package com.doctor.br.activity.medical;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.InputType;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.PropertyPreFilter;
import com.doctor.br.activity.MainActivity;
import com.doctor.br.activity.chatmain.ChatMainActivity;
import com.doctor.br.activity.mine.QualificationActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.AuthStateResult;
import com.doctor.br.bean.DataCacheType;
import com.doctor.br.bean.event.RefreshDoctorDataEvent;
import com.doctor.br.bean.medical.MedicineDetailMsgBean;
import com.doctor.br.bean.medical.OrderMsgBean;
import com.doctor.br.bean.medical.SavePrescriptionSuccessedBean;
import com.doctor.br.db.entity.DataCache;
import com.doctor.br.db.utils.DataCacheDaoUtil;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.BroadcastAction;
import com.doctor.br.utils.DateUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.ShareUtils;
import com.doctor.br.view.FlowLayout;
import com.doctor.br.view.NoDoubleClickBtn;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.listener.OnButtonClickListener;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.AlertDialog;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.InputDialog;
import org.newapp.ones.base.widgets.LoadingDialog;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Author:sunxiaxia
 * createdDate:2017/12/18
 * description:预览用药
 */

public class PreviewMedicationActivity extends ActionBarActivity {
    public static String FROM_QUICK_PRESCRIPTION = "from_quick_prescription";
    //短信开方
    public static String FROM_SMS_PRESCRIPTION = "from_sms_prescription";
    private static String EXTRA_FROM = "extra_from";

    public static void launch(Context context, String drugForm, OrderMsgBean orderMsgBean, String from) {
        Intent intent = new Intent(context, PreviewMedicationActivity.class);
        intent.putExtra(PublicParams.DRUG_FROM, drugForm);
        intent.putExtra("orderMsgBean", orderMsgBean);
        intent.putExtra(EXTRA_FROM, from);
        context.startActivity(intent);
    }

    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.tv_age)
    TextView tvAge;
    @BindView(R.id.tv_sex)
    TextView tvSex;
    @BindView(R.id.tv_pregant)
    TextView tvPregant;
    @BindView(R.id.tv_disease)
    TextView tvDisease;
    @BindView(R.id.flowLayout)
    FlowLayout flowLayout;
    @BindView(R.id.tv_tip_medicine_type)
    TextView tvTipMedicineType;
    @BindView(R.id.tv_medicine_type)
    TextView tvMedicineType;
    @BindView(R.id.ll_specification)
    LinearLayout llSpecification;
    @BindView(R.id.tv_specification)
    TextView tvSpecification;
    @BindView(R.id.tv_tip_dosage)
    TextView tvTipDosage;
    @BindView(R.id.tv_dose_count)
    TextView tvDoseCount;
    @BindView(R.id.tv_dose_day)
    TextView tvDoseDay;
    @BindView(R.id.tv_dose_times)
    TextView tvDoseTimes;
    @BindView(R.id.tv_drink_time)
    TextView tvDrinkTime;
    @BindView(R.id.ll_dosage)
    LinearLayout llDosage;
    @BindView(R.id.tv_tip_avoid)
    TextView tvTipAvoid;
    @BindView(R.id.tv_avoid)
    TextView tvAvoid;
    @BindView(R.id.tv_tip_guid)
    TextView tvTipGuid;
    @BindView(R.id.tv_guid)
    TextView tvGuid;
    @BindView(R.id.tv_money)
    TextView tvMoney;
    @BindView(R.id.tv_doctor_name)
    TextView tvDoctorName;
    @BindView(R.id.tv_time)
    TextView tvTime;
    @BindView(R.id.ll_daijian)
    LinearLayout llDaijian;
    @BindView(R.id.tv_daijian)
    TextView tvDaiJian;
    @BindView(R.id.ll_daijian_preference)
    LinearLayout llDaiJianPreference;
    @BindView(R.id.tv_daijian_preference)
    TextView tvDaiJianPreference;
    @BindView(R.id.btn_send_patient)
    NoDoubleClickBtn btnSendPatient;
    @BindView(R.id.tv_secret_type_input)
    TextView tvSecretTypeInput;
    @BindView(R.id.ll_use_method)
    LinearLayout llUseMethod;
    @BindView(R.id.tv_use_method)
    TextView tvUseMethod;

//    @BindView(R.id.tv_total_weight)
//    TextView tvTotalWeight;
//    @BindView(R.id.tv_make_description)
//    TextView tvMakeDescription;

    // 辅料显示相关控件（动态创建）
    private LinearLayout llAuxiliaryMaterial;
    private TextView tvAuxiliaryMaterial;

    private OrderMsgBean mOrderMsgBean;
    private String mDrugForm;
    private AuthStateResult authState;
    private RequestCallBack authCallBack;
    private RequestCallBack prescriptionCallBack;

    //短信开方发送短息回调
    private RequestCallBack sendMessagePrescribeCallBack;

    //当前认证状态 -1 未知 1 认证成功 2 审核中 3 未认证 4 认证失败
    private String currentState = "-1";
    //剩下的试用天数
    private String stockDays = "-1";
    //医生id，患者id
    private String userId, patientId;
    //是否在获取完认证状态后直接发送药方给患者，默认为false不发送
    private boolean isSend;
    //用户是否前往认证的对话框
    private ConfirmDialog authDialog;
    //认证中的状态对话框
    private AlertDialog authingDialog;
    private String mFrom = "";
    private String orderId;
    private String shareToPatientUrl;

    private InputDialog inputDialog;
    //保存输入患者的手机号
    private String patientPhone;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_preview_medication);
        setActionBarTitle("用药预览");
        setActionBarRightBtnText("药品明细");
        patientId = SharedPreferenceUtils.getString(mContext, PublicParams.PATIENT_USRE_ID);
        userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        OrderMsgBean orderMsgBean = (OrderMsgBean) getIntent().getSerializableExtra("orderMsgBean");
        mDrugForm = getIntent().getStringExtra(PublicParams.DRUG_FROM);
        mFrom = getIntent().getStringExtra(EXTRA_FROM);
        setViewData(orderMsgBean);
        EventBusUtils.register(this);

        if (mFrom.equals(FROM_QUICK_PRESCRIPTION)) {
            btnSendPatient.setText("发送到患者微信");
        } else if (mFrom.equals(FROM_SMS_PRESCRIPTION)) {
            btnSendPatient.setText("发送到患者短信");
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        mLoadingDialog = null;
        try {
            requestAuthState();
        } catch (Exception e) {
            LogUtils.e(PreviewMedicationActivity.class, e.getMessage());
        }
    }

    /**
     * 请求认证状态
     */
    private void requestAuthState() {
        authCallBack = addHttpPostRequest(HttpUrlManager.AUTHENTICATION_STATE_CODE
                , null, AuthStateResult.class, this);
    }


    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        MedicationDrugDetailsActivity.launch(this,mOrderMsgBean);
    }

    /**
     * @param orderMsgBean 显示医案信息
     */
    private void setViewData(OrderMsgBean orderMsgBean) {
        if (orderMsgBean != null) {
            mOrderMsgBean = orderMsgBean;
            tvName.setText("姓名：" + orderMsgBean.getTakerName());
            tvAge.setText("年龄：" + orderMsgBean.getTakerAge());
            if ("1".equals(orderMsgBean.getTakerSex())) {
                tvSex.setText("性别：" + "男");
            } else {
                tvSex.setText("性别：" + "女");
            }
            if ("1".equals(orderMsgBean.getTakerIsPregnant())) {
                tvPregant.setText("怀孕");
            } else {
                tvPregant.setText("");
            }
            tvDisease.setText(orderMsgBean.getDescription());//辨证
            String drugForm = orderMsgBean.getDrugForm();
            SpannableStringBuilder ssbDrugForm = new SpannableStringBuilder(drugForm);
            if (!TextUtils.isEmpty(orderMsgBean.getFormDetailMsg().getName())) {//添加  （厂家）
                ssbDrugForm.append("(");
                ssbDrugForm.append(orderMsgBean.getFormDetailMsg().getName());
                ssbDrugForm.append(")");
            }
            tvMedicineType.setText(ssbDrugForm);//剂型
            
            // 显示规格信息（根据剂型判断是否显示）
            String specificationText = getSpecificationText(orderMsgBean, drugForm);
            if (!TextUtils.isEmpty(specificationText) && shouldShowSpecification(drugForm)) {
                llSpecification.setVisibility(View.VISIBLE);
                tvSpecification.setText(specificationText);
            } else {
                llSpecification.setVisibility(View.GONE);
            }
            
            // 显示辅料信息（只有膏方剂型且有辅料数据时才显示）
            showAuxiliaryMaterial(orderMsgBean, drugForm);
            
            //用法用量的显示
            if (PublicParams.DOSAGEFORM_GRANULE.equals(drugForm)
                    || PublicParams.DOSAGEFORM_SLICES.equals(drugForm)
                    || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm)
                    || PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(drugForm)) {
//                tvTotalWeight.setVisibility(View.GONE);
//                tvMakeDescription.setVisibility(View.GONE);
                //颗粒等
                tvDoseCount.setText(createSpannable("共", orderMsgBean.getTotalPreNum(), "剂"));
                tvDoseDay.setText(createSpannable("每日", orderMsgBean.getDayPreNum(), "剂"));
                tvDoseTimes.setText(createSpannable("每剂分", orderMsgBean.getPreTimes(), "次服用"));
                tvDrinkTime.setText(orderMsgBean.getUseTime() + "服用");
            } else if (PublicParams.DOSAGEFORM_POWDER.equals(drugForm)
                    || PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(drugForm)
                    || PublicParams.DOSAGEFORM_WATERED_PILL.equals(drugForm)
                    || PublicParams.DOSAGEFORM_HONEYED_PILL.equals(drugForm)
                    || PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(drugForm)) {
                //散剂、膏方、水丸、蜜丸、胶囊
//                tvTotalWeight.setVisibility(View.VISIBLE);
//                tvMakeDescription.setVisibility(View.VISIBLE);
//                tvTotalWeight.setText(orderMsgBean.getBalanceWeight());
//                if (TextUtils.isEmpty(orderMsgBean.getMakeDesc())) {//为空
//                    tvMakeDescription.setVisibility(View.GONE);
//                } else {
//                    tvMakeDescription.setText(orderMsgBean.getMakeDesc());
//                }
                tvDoseCount.setText(createSpannable("每日", orderMsgBean.getMrjc(), "次"));

                // 获取每次用量的显示
                SpannableStringBuilder perDoseText = getDosageDisplayText(orderMsgBean, drugForm);
                tvDoseDay.setText(perDoseText);
                
                tvDoseTimes.setText(createSpannable("预计可用", orderMsgBean.getTakeDays(), "天"));
                tvDrinkTime.setText(orderMsgBean.getUseTime() + "服用");
            }
            //设置服药禁忌
            tvAvoid.setText(TextUtils.isEmpty(orderMsgBean.getContraindication())
                    ? "无" : orderMsgBean.getContraindication());
            //服药说明
            tvGuid.setText(TextUtils.isEmpty(orderMsgBean.getInstructions())
                    ? "无" : orderMsgBean.getInstructions());
            if (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm)) {
                llDaijian.setVisibility(View.VISIBLE);
                llDaiJianPreference.setVisibility(View.VISIBLE);
//                if (orderMsgBean.isUseDaiJian()){
                    tvDaiJian.setText("代煎");
                    tvDaiJianPreference.setText(orderMsgBean.getMakeMethod());
//                }else {
//                    tvDaiJian.setText("自煎");
//                }
            } else {
                llDaijian.setVisibility(View.GONE);
                llDaiJianPreference.setVisibility(View.GONE);
            }

            //用药方法
            if (PublicParams.DOSAGEFORM_SLICES.equals(drugForm) ||
            PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm) ||
            PublicParams.DOSAGEFORM_POWDER.equals(drugForm)) {
                llUseMethod.setVisibility(View.VISIBLE);
                tvUseMethod.setText(orderMsgBean.getMode());
            } else  {
                llUseMethod.setVisibility(View.GONE);
            }

            String secretType = "正常处方";
            if ("1".equals(orderMsgBean.getIsSecrecy())) {
                secretType = "保密处方";
            } else if ("2".equals(orderMsgBean.getIsSecrecy())) {
                secretType = "剂量保密";
            }

            tvSecretTypeInput.setText(secretType);

            tvMoney.setText("订单金额：" + orderMsgBean.getTotalPrice());//总计
            //设置医师姓名
            setSpannableNameOrTime(tvDoctorName, "医医师师"
                    , SharedPreferenceUtils.getString(this, PublicParams.USER_NAME));
            //设置医师姓名
            setSpannableNameOrTime(tvTime, "日日期期", DateUtils.getNowDate("yyyy-MM-dd"));
            List<MedicineDetailMsgBean> medicines = orderMsgBean.getPreDetailList();
            if (medicines != null && medicines.size() > 0) {
                showAddMedicines(flowLayout, medicines);
            }
        } else {
            mOrderMsgBean = new OrderMsgBean();
        }

    }

    /**
     * 生成，如 “共 1 剂”的样式（1为蓝色）
     *
     * @param leftContent
     * @param centerContent
     * @param rightContent
     * @return
     */
    public SpannableStringBuilder createSpannable(String leftContent, String centerContent, String rightContent) {
        SpannableStringBuilder ssb = new SpannableStringBuilder(leftContent);
        if (!TextUtils.isEmpty(centerContent)) {
            ssb.append(" " + centerContent + " ");
            ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_tab_checked))
                    , leftContent.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        if (!TextUtils.isEmpty(rightContent)) {
            ssb.append(rightContent);
        }
        return ssb;
    }

    public SpannableStringBuilder createSpannable(String leftContent, String centerContent, String rightContent, String redContent) {
        SpannableStringBuilder ssb = new SpannableStringBuilder(leftContent);
        if (!TextUtils.isEmpty(centerContent)) {
            ssb.append(centerContent);
            ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_tab_checked)),
                    leftContent.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        if (!TextUtils.isEmpty(rightContent)) {
            ssb.append(rightContent);
        }

        // 添加红色文字
        if (!TextUtils.isEmpty(redContent)) {
            ssb.append(" ");
            int startIndex = ssb.length();
            ssb.append(redContent);
            ssb.setSpan(new ForegroundColorSpan(Color.RED),
                    startIndex, ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }

        return ssb;
    }

    /**
     * 设置医生姓名，日期，如contentTip = 医医师师，中间的“医师”设置为白色，这样做是为了让冒号对齐
     *
     * @param contentTip
     * @param content
     */
    private void setSpannableNameOrTime(TextView tv, String contentTip, String content) {
        SpannableStringBuilder ssbName = new SpannableStringBuilder(contentTip);//这样做是为了让冒号对齐
        ssbName.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_white))
                , 1, ssbName.length() - 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        ssbName.append("：");
        ssbName.append(content);
        tv.setText(ssbName);
    }

    /**
     * 显示添加的药材
     */
    private void showAddMedicines(FlowLayout medicineContainer, List<MedicineDetailMsgBean> medicines) {
        if (medicines != null && medicines.size() > 0) {
            medicineContainer.setVisibility(View.VISIBLE);
            medicineContainer.removeAllViews();
            for (MedicineDetailMsgBean medicine : medicines) {
                TextView tv = new TextView(mContext);
                tv.setLayoutParams(new FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT
                        , FlowLayout.LayoutParams.WRAP_CONTENT));
                tv.setPadding(DensityUtils.dip2px(mContext, 10)
                        , DensityUtils.dip2px(mContext, 5)
                        , DensityUtils.dip2px(mContext, 15)
                        , DensityUtils.dip2px(mContext, 5));
                tv.setTextColor(getResources().getColor(R.color.br_color_theme_text));
                tv.setTextSize(15);
//            tv.setTextSize(TypedValue.COMPLEX_UNIT_PX,DensityUtils.sp2px(mContext,15));
                String content = medicine.getDrugName() + medicine.getDose() + medicine.getUnit();
                SpannableStringBuilder ssb = new SpannableStringBuilder(content);
                if (PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm) ||
                    PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)) {//饮片
                    if (!TextUtils.isEmpty(medicine.getUseMethod())) {//特殊煎药方式
                        String subContent = "(" + medicine.getUseMethod() + ")";
                        ssb.append(subContent);
                    }
                }
                ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_red_ef4d3b))
                        , content.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                ssb.setSpan(new AbsoluteSizeSpan(DensityUtils.sp2px(mContext, 12))
                        , content.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                tv.setText(ssb);
                medicineContainer.addView(tv);
            }
        } else {
            medicineContainer.setVisibility(View.GONE);
        }

    }


    @OnClick(R.id.btn_send_patient)
    public void onViewClicked() {
        if (mLoadingDialog == null) {
            mLoadingDialog = LoadingDialog.getInstance(this);
        }
        if ("-1".equals(currentState)) {
            isSend = true;
            requestAuthState();
            return;
        }
        showDialogAndSend();
    }


    /**
     * 根据认证状态判断需要弹出提示框还是直接发送给患者
     */
    private void showDialogAndSend() {
        //获取试用天数
        int intStockDay;
        try {
            intStockDay = Integer.parseInt(stockDays);
        } catch (Exception e) {
            intStockDay = -1;
        }
        //认证成功或认证中状态下可以直接发送
        if ("1".equals(currentState) ||
                ("2".equals(currentState) && intStockDay > 0)) {
            sendToPatient();
            return;
        }

        authDialog = ConfirmDialog.getInstance(this)
                .setPositiveText("立即认证")
                .setNavigationText("暂不认证")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        authDialog.dismiss();
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        authDialog.dismiss();
                        Intent intent = new Intent(mContext, QualificationActivity.class);
                        intent.putExtra(QualificationActivity.STATE, authState.getCurrentAuthenticationState());
                        intent.putExtra(QualificationActivity.HISTORY_STATE, authState.getIsAuthentication());
                        startActivity(intent);
                    }
                });

        //还有试用天数
        if (intStockDay > 0) {
            authDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                @Override
                public void onNavigationBtnClicked(View view) {
                    sendToPatient();
                    authDialog.dismiss();
                }

                @Override
                public void onPositiveBtnClicked(View view) {
                    authDialog.dismiss();
                    Intent intent = new Intent(mContext, QualificationActivity.class);
                    intent.putExtra(QualificationActivity.STATE, authState.getCurrentAuthenticationState());
                    intent.putExtra(QualificationActivity.HISTORY_STATE, authState.getIsAuthentication());
                    startActivity(intent);
                }
            });
            if ("3".equals(currentState)) {
                //未认证
                authDialog.setDialogContent("您的试用期剩余" + intStockDay + "天，为了不影响您的正常使用，请尽快上传资质认证。");
            } else {
                //认证失败
                authDialog.setDialogContent("认证审核失败，试用期剩余" + intStockDay + "天，为了不影响您的正常使用，请重新上传资质认证。");
            }
        } else {
            //没有试用天数
            if ("3".equals(currentState)) {
                //未认证
                authDialog.setDialogContent("认证通过后可使用该功能");
            } else if ("4".equals(currentState)) {
                //认证失败
                authDialog.setDialogContent("认证审核失败，请重新认证。");
            } else {
                //认证中
                if (authingDialog == null) {
                    authingDialog = AlertDialog.getInstance(this)
                            .setDialogContent("认证审核中，审核通过后可使用该功能。")
                            .setPositiveText("确认");
                }
                authingDialog.show();
                return;
            }
        }
        authDialog.show();
    }

    private boolean isAllMakeCost = false;

    /**
     * 发送给患者
     */
    public void sendToPatient() {

        if (TextUtils.equals(mFrom, FROM_QUICK_PRESCRIPTION) && !TextUtils.isEmpty(shareToPatientUrl)) {
            //微信开方
            shareToWeixin();
            return;
        }

        if (TextUtils.equals(mFrom, FROM_SMS_PRESCRIPTION) && !TextUtils.isEmpty(orderId) && !TextUtils.isEmpty(patientPhone)) {
            //短信开方
            shareToSms(orderId, patientPhone);
            return;
        }

        if (TextUtils.equals(mFrom, FROM_SMS_PRESCRIPTION)) {
            //如果为短信开方 则先输入用户手机号
            inputDialog = InputDialog.getInstance(this)
                    .setInputTitle("请输入患者手机号")
                    .setInputHint("输入手机号")
                    .setInputMinHeight(30)
                    .setInputMinLines(1)
                    .setInputMaxLength(11)
                    .setNegativeText("取消")
                    .setPositiveText("发送")
                    .setOnButtonClickListener(new OnButtonClickListener() {
                        @Override
                        public void onPositiveClick(View view, CharSequence editText) {
                            String mobile = editText.toString().trim();

                            if (mobile.isEmpty()){
                                ToastUtils.showShortMsg(mContext, "请输入患者手机号");
                                return;
                            }

                            if (mobile.length() != 11){
                                ToastUtils.showShortMsg(mContext, "请输入正确的手机号");
                                return;
                            }

                            patientPhone = mobile;

                            sendToPatientRequestAction();
                            closeInputDialog();
                        }

                        @Override
                        public void onNegativeClick(View view, CharSequence editText) {
                            closeInputDialog();
                        }
                    });
            inputDialog.setEditTextInputType(InputType.TYPE_CLASS_PHONE);
            inputDialog.show();
        } else {
            //保存药方接口调用
            sendToPatientRequestAction();
        }
    }

    private void sendToPatientRequestAction(){
        HashMap map = new HashMap();
//        mOrderMsgBean.setTotalPrice(null);//保存药方不需要此参数
//        mOrderMsgBean.setFormDetailMsg(null);
//        mOrderMsgBean.setFormList(null);
        if ((TextUtils.equals("饮片", mOrderMsgBean.getDrugForm()) ||
                TextUtils.equals("代煎", mOrderMsgBean.getDrugForm())) &&
                !TextUtils.isEmpty(mOrderMsgBean.getMakeCost())) {
//            if (mOrderMsgBean.isUseDaiJian()) {
            if (TextUtils.equals("代煎", mOrderMsgBean.getDrugForm())){
                if (!isAllMakeCost) {
                    String num = TextUtils.isEmpty(mOrderMsgBean.getTotalPreNum())
                            ? "7" : mOrderMsgBean.getTotalPreNum();//付数
                    String makeFeePre = TextUtils.isEmpty(mOrderMsgBean.getMakeCost())
                            ? "0.00" : mOrderMsgBean.getMakeCost();//制作费
                    mOrderMsgBean.setMakeCost(new BigDecimal(makeFeePre).multiply(new BigDecimal(num)).setScale(2).toString());
                    isAllMakeCost = true;
                }
            } else {
                mOrderMsgBean.setMakeCost("0.00");
            }
        }
        map.put("order", JSON.toJSONString(mOrderMsgBean, new PropertyPreFilter() {
            //保存药方时不需要这些参数
            @Override
            public boolean apply(JSONSerializer jsonSerializer, Object o, String name) {
                if ("leftP".equals(name) || "rightP".equals(name) || "formList".equals(name)
                        || "formDetailMsg".equals(name) || "diagnosed".equals(name)
                        || "totalPrice".equals(name) || "makeDesc".equals(name) || "useDaiJian".equals(name) ||
                "weight".equals(name)) {
                    return false;
                }
                return true;
            }
        }));
        map.put("apiVer", "4");//4以下

        LogUtils.i(PreviewMedicationActivity.class, "提交的map值如下:" + map);
//        //打印map中所有字段的值
//        for (Object key : map.keySet()) {
//            LogUtils.i(PreviewMedicationActivity.class, key + ":" + map.get(key));
//        }

        //短信开方 和 微信开方，都用000440来保存快速处方
        if (TextUtils.equals(mFrom, FROM_QUICK_PRESCRIPTION ) || TextUtils.equals(mFrom, FROM_SMS_PRESCRIPTION)) {
            if (TextUtils.equals(mFrom, FROM_QUICK_PRESCRIPTION)){
                map.put("smsOrder","0");
            }
            if (TextUtils.equals(mFrom, FROM_SMS_PRESCRIPTION)){
                map.put("smsOrder", "1");
            }
            prescriptionCallBack = addHttpPostRequest(HttpUrlManager.QUICK_SAVE_PRESCRIPTION,
                    map, SavePrescriptionSuccessedBean.class, this);
        } else {
            prescriptionCallBack = addHttpPostRequest(HttpUrlManager.SAVE_PRERSCRIPTION,
                    map, SavePrescriptionSuccessedBean.class, this);
        }
    }

    public void closeInputDialog() {
        if (inputDialog != null) {
            inputDialog.dismiss();
        }
    }

    private void shareToWeixin() {
        String name = SharedPreferenceUtils.getString(AppContext.getContext(), PublicParams.USER_NAME);
        String title = name + "大夫已给你开好药方！";
        String content = name + "大夫给"+mOrderMsgBean.getTakerName()+"的药方,订单号："+orderId;
        new ShareUtils(this).shareToWeiXin(title, content, "http://img.haoniuzhongyi.top:18088/filestore/images/logo.png", shareToPatientUrl);
    }

    private void shareToSms(String orderId, String mobile) {
        //创建map参数
        Map<String, String> postData = new HashMap<>();
        postData.put("apiVer", "4");
        postData.put("orderId", orderId);
        postData.put("mobile", mobile);
        postData.put("sys_type", "1");

        sendMessagePrescribeCallBack = addHttpPostRequest(HttpUrlManager.SMS_PRESCRIPTION,
                postData, ResponseResult.class, this);
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.SAVE_PRERSCRIPTION:
                if (result.isRequestSuccessed()) {
                    //处理缓存的药方信息
                    handleCachePresMsg();
                    //通过广播传递消息去显示交流页面
                    Intent filter = new Intent(BroadcastAction.SET_CHAT_MAIN_POSITION);
                    filter.putExtra(ChatMainActivity.POSITION, 1);
                    filter.putExtra(ChatMainActivity.ISREPLACE, true);
                    sendBroadcast(filter);
                    ToastUtils.showShortMsg(PreviewMedicationActivity.this, "发送成功！");
                    this.finish();
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                btnSendPatient.setClickable(true);
                break;

            case HttpUrlManager.QUICK_SAVE_PRESCRIPTION:
                if (result.isRequestSuccessed()) {
                    SavePrescriptionSuccessedBean bean = (SavePrescriptionSuccessedBean) result.getBodyObject();
                    //处理缓存的药方信息
                    orderId = bean.getPreId();
                    shareToPatientUrl = bean.getShareUrl();

                    if(mFrom.equals(FROM_QUICK_PRESCRIPTION)) {
                        //微信开方
                        shareToWeixin();
                    } else if (mFrom.equals(FROM_SMS_PRESCRIPTION)) {
                        //短信开方
                        shareToSms(orderId, patientPhone);
                    }

                    handleCacheQuickPresMsg();
//                    ToastUtils.showShortMsg(PreviewMedicationActivity.this, "发送成功！");
//                    this.finish();
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                btnSendPatient.setClickable(true);
                break;
            case HttpUrlManager.SMS_PRESCRIPTION:
                //短信开方
                if (result.isRequestSuccessed()){
                    ToastUtils.showShortMsg(PreviewMedicationActivity.this, "发送成功！");
                    Intent intent = new Intent(mContext, MainActivity.class);

                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP |
                            Intent.FLAG_ACTIVITY_SINGLE_TOP);
                    startActivity(intent);

                    finish();
                }else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.AUTHENTICATION_STATE_CODE:
                if (result.isRequestSuccessed()) {
                    authState = (AuthStateResult) result.getBodyObject();
                    if (authState != null) {
                        //如果之前为老用户或认证成功过，则认为认证成功
                        if ("1".equals(authState.getIsOld()) || "1".equals(authState.getIsAuthentication())) {
                            currentState = "1";
                        } else {
                            currentState = authState.getCurrentAuthenticationState();
                            stockDays = authState.getStockDay();
                        }
                        if (isSend) {
                            isSend = false;
                            showDialogAndSend();
                        }
                        return;
                    }
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                isSend = false;
                currentState = "-1";
                stockDays = "-1";
                break;
        }
    }

    /**
     * 处理缓存的药方信息
     */
    private void handleCachePresMsg() {
        DataCacheDaoUtil dataCacheDaoUtil = DataCacheDaoUtil.getInstance();
        DataCache cachePresMsg = dataCacheDaoUtil
                .select(DataCacheType.CACHE_PRESCRIPTION_MSG, userId + "_" + patientId);
        //本地保存发送复诊单状态
        if (cachePresMsg != null) {
            OrderMsgBean medicationParams = JSON.parseObject(cachePresMsg.getValue(), OrderMsgBean.class);
            if ("1".equalsIgnoreCase(medicationParams.getIsAutoSend())) {
                SharedPreferenceForeverUtils.putBoolean(mContext, PublicParams.IS_AUTO_SEND_FZD, true);
            } else if ("0".equalsIgnoreCase(medicationParams.getIsAutoSend())) {
                SharedPreferenceForeverUtils.putBoolean(mContext, PublicParams.IS_AUTO_SEND_FZD, false);
            }
        }
        //药方成功发送以后清空缓存的药方信息
        dataCacheDaoUtil.clearCache(DataCacheType.CACHE_PRESCRIPTION_MSG
                , userId + "_" + patientId);
    }

    /**
     * 处理缓存的药方信息
     */
    private void handleCacheQuickPresMsg() {
        DataCacheDaoUtil dataCacheDaoUtil = DataCacheDaoUtil.getInstance();
//        DataCache cachePresMsg = dataCacheDaoUtil
//                .select(DataCacheType.CACHE_QUICK_PRESCRIPTION_MSG, userId + "_" + mOrderMsgBean.getTakerName());
//        //本地保存发送复诊单状态
//        if (cachePresMsg != null) {
//            OrderMsgBean medicationParams = JSON.parseObject(cachePresMsg.getValue(), OrderMsgBean.class);
//            if ("1".equalsIgnoreCase(medicationParams.getIsAutoSend())) {
//                SharedPreferenceForeverUtils.putBoolean(mContext, PublicParams.IS_AUTO_SEND_FZD, true);
//            } else if ("0".equalsIgnoreCase(medicationParams.getIsAutoSend())) {
//                SharedPreferenceForeverUtils.putBoolean(mContext, PublicParams.IS_AUTO_SEND_FZD, false);
//            }
//        }
        //药方成功发送以后清空缓存的药方信息
        dataCacheDaoUtil.clearCache(DataCacheType.CACHE_QUICK_PRESCRIPTION_MSG
                , userId + "_" + mOrderMsgBean.getTakerName());
        DataCache cachePresMsg = DataCacheDaoUtil.getInstance()
                .select(DataCacheType.CACHE_QUICK_PRESCRIPTION_MSG, userId + "_" +  mOrderMsgBean.getTakerName());
        if (cachePresMsg!=null){
            System.out.println("删除缓存后获取缓存信息===="+cachePresMsg.getValue());
        }else {
            LogUtils.i(PreviewMedicationActivity.class,"=======删除缓存成功");
        }
    }

    /**
     * 资质认证界面提交认证后的返回值
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void refreshState(RefreshDoctorDataEvent refreshDoctorDataEvent) {
        if (refreshDoctorDataEvent != null) {
            currentState = refreshDoctorDataEvent.getIsAuthentication();
        }
    }

    /**
     *
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void finishActivity(FinishActivityEvent event) {
        if (event != null && event.isCurrentActivity(getClass().getName())) {
            handleCacheQuickPresMsg();
            finish();
        }
    }

    @Override
    public void onBack(Activity activity) {
        super.onBack(activity);
    }

    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        cancelRequest(prescriptionCallBack);
        cancelRequest(authCallBack);
        cancelRequest(sendMessagePrescribeCallBack);
        super.onDestroy();
    }

    /**
     * 判断是否应该显示规格信息
     * @param drugForm 剂型
     * @return true表示应该显示规格，false表示不显示规格
     */
    private boolean shouldShowSpecification(String drugForm) {
        // 当剂型为饮片、颗粒、外用中药、代煎时，不显示规格行
        return !(PublicParams.DOSAGEFORM_SLICES.equals(drugForm) ||
                PublicParams.DOSAGEFORM_GRANULE.equals(drugForm) ||
                PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(drugForm) ||
                PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(drugForm));
    }

    /**
     * 获取规格显示文本
     * @param orderMsgBean 订单数据
     * @param drugForm 剂型
     * @return 规格显示文本，如果没有规格数据则返回空字符串
     */
    private String getSpecificationText(OrderMsgBean orderMsgBean, String drugForm) {
        if (orderMsgBean == null || TextUtils.isEmpty(drugForm)) {
            return "";
        }

        String specification = "";

        // 根据不同剂型获取对应的规格数据
        // 统一使用packageSpec字段获取规格信息
        specification = orderMsgBean.getPackageSpec();

        return TextUtils.isEmpty(specification) ? "" : specification;
    }
    
    /**
     * 获取每次用量的显示文本
     * @param orderMsgBean 订单数据
     * @param drugForm 剂型
     * @return 格式化后的用量显示文本
     */
    private SpannableStringBuilder getDosageDisplayText(OrderMsgBean orderMsgBean, String drugForm) {
        if (orderMsgBean == null || TextUtils.isEmpty(drugForm)) {
            return createSpannable("每次", orderMsgBean.getMcjk(), "g");
        }
        
        // 蜜丸剂型处理
        if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(drugForm)) {
            String pillSpec = orderMsgBean.getPackageSpec();
            String unitCount = orderMsgBean.getPillUnitCount();
            
            if (!TextUtils.isEmpty(pillSpec) && !TextUtils.isEmpty(unitCount)) {
                // 解析规格数据，获取单位和每单位的克数
                String unit = "";
                double gramPerUnit = 0.0;
                
                // 解析规格字符串，如"3g/丸"
                if (pillSpec.contains("g/")) {
                    String[] parts = pillSpec.split("g/");
                    if (parts.length == 2) {
                        try {
                            gramPerUnit = Double.parseDouble(parts[0]);
                            unit = parts[1];
                        } catch (NumberFormatException e) {
                            // 解析失败，使用默认显示
                        }
                    }
                }
                
                if (!TextUtils.isEmpty(unit) && gramPerUnit > 0) {
                    // 计算总克数
                    try {
                        int count = Integer.parseInt(unitCount);
                        double totalGram = count * gramPerUnit;
                        String gramText = String.format("%.1f", totalGram);
                        // 返回格式：每次 x 丸 (xg)
                        return createSpannable("每次", unitCount, unit, "(" + gramText + "g)");
                    } catch (NumberFormatException e) {
                        // 解析失败，使用默认显示
                    }
                }
            }
            // 没有规格数据或解析失败，使用默认的克数显示
            return createSpannable("每次", orderMsgBean.getMcjk(), "g");
        }
        
        // 胶囊剂型处理
        if (PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(drugForm)) {
            String capsuleSpec = orderMsgBean.getPackageSpec(); // 统一使用packageSpec字段
            String unitCount = orderMsgBean.getCapsuleUnitCount();
            
            if (!TextUtils.isEmpty(capsuleSpec) && !TextUtils.isEmpty(unitCount)) {
                // 解析规格数据
                String unit = "";
                double gramPerUnit = 0.0;
                
                // 解析规格字符串，如"0.5g/粒"
                if (capsuleSpec.contains("g/")) {
                    String[] parts = capsuleSpec.split("g/");
                    if (parts.length == 2) {
                        try {
                            gramPerUnit = Double.parseDouble(parts[0]);
                            unit = parts[1];
                        } catch (NumberFormatException e) {
                            // 解析失败，使用默认显示
                        }
                    }
                }
                
                if (!TextUtils.isEmpty(unit) && gramPerUnit > 0) {
                    // 计算总克数
                    try {
                        int count = Integer.parseInt(unitCount);
                        double totalGram = count * gramPerUnit;
                        String gramText = String.format("%.1f", totalGram);
                        // 返回格式：每次 x 粒 (xg)
                        return createSpannable("每次", unitCount, unit, "(" + gramText + "g)");
                    } catch (NumberFormatException e) {
                        // 解析失败，使用默认显示
                    }
                }
            }
            
            // 兼容旧的胶囊显示逻辑
            if (!TextUtils.isEmpty(orderMsgBean.getMcDose())) {
                return createSpannable("每次", orderMsgBean.getMcjk(), "g", "(" + orderMsgBean.getMcDose() + "颗)");
            }
            
            // 默认显示
            return createSpannable("每次", orderMsgBean.getMcjk(), "g");
        }
        
        // 水丸剂型处理
        if (PublicParams.DOSAGEFORM_WATERED_PILL.equals(drugForm)) {
            String waterPillSpec = orderMsgBean.getPackageSpec(); // 统一使用packageSpec字段
            String unitCount = orderMsgBean.getWaterPillUnitCount();
            
            if (!TextUtils.isEmpty(waterPillSpec) && !TextUtils.isEmpty(unitCount)) {
                // 解析规格数据
                String unit = "";
                double gramPerUnit = 0.0;
                
                // 解析规格字符串，如"3g/丸"
                if (waterPillSpec.contains("g/")) {
                    String[] parts = waterPillSpec.split("g/");
                    if (parts.length == 2) {
                        try {
                            gramPerUnit = Double.parseDouble(parts[0]);
                            unit = parts[1];
                        } catch (NumberFormatException e) {
                            // 解析失败，使用默认显示
                        }
                    }
                }
                
                if (!TextUtils.isEmpty(unit) && gramPerUnit > 0) {
                    // 计算总克数
                    try {
                        int count = Integer.parseInt(unitCount);
                        double totalGram = count * gramPerUnit;
                        String gramText = String.format("%.1f", totalGram);
                        // 返回格式：每次 x 丸 (xg)
                        return createSpannable("每次", unitCount, unit, "(" + gramText + "g)");
                    } catch (NumberFormatException e) {
                        // 解析失败，使用默认显示
                    }
                }
            }
            // 没有规格数据或解析失败，使用默认的克数显示
            return createSpannable("每次", orderMsgBean.getMcjk(), "g");
        }
        
        // 膏方剂型处理
        if (PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(drugForm)) {
            String creamFormulaSpec = orderMsgBean.getPackageSpec(); // 统一使用packageSpec字段
            String unitCount = orderMsgBean.getCreamFormulaUnitCount();
            
            if (!TextUtils.isEmpty(creamFormulaSpec) && !TextUtils.isEmpty(unitCount)) {
                // 解析规格数据
                String unit = "";
                double gramPerUnit = 0.0;
                
                // 解析规格字符串，如"10g/勺"、"280ml/瓶"
                if (creamFormulaSpec.contains("g/") || creamFormulaSpec.contains("ml/")) {
                    String[] parts;
                    if (creamFormulaSpec.contains("g/")) {
                        parts = creamFormulaSpec.split("g/");
                    } else {
                        parts = creamFormulaSpec.split("ml/");
                    }

                    if (parts.length == 2) {
                        try {
                            gramPerUnit = Double.parseDouble(parts[0]);
                            unit = parts[1];
                        } catch (NumberFormatException e) {
                            // 解析失败，使用默认显示
                        }
                    }
                }
                
                if (!TextUtils.isEmpty(unit) && gramPerUnit > 0) {
                    // 特殊处理：当单位为"瓶"时
                    if ("瓶".equals(unit)) {
                        // 瓶装膏方用户输入的是克数，直接显示克数，不显示瓶数
                        // 让它走默认的mcjk显示逻辑，显示为"每次 30g"
                        return createSpannable("每次", orderMsgBean.getMcjk(), "g");
                    } else {
                        // 其他单位时，计算总克数并显示格式：每次 x 勺 (xg)
                        try {
                            int count = Integer.parseInt(unitCount);
                            double totalGram = count * gramPerUnit;
                            String gramText = String.format("%.1f", totalGram);
                            return createSpannable("每次", unitCount, unit, "(" + gramText + "g)");
                        } catch (NumberFormatException e) {
                            // 解析失败，使用默认显示
                        }
                    }
                }
            }
            
            // 兼容旧的膏方显示逻辑或无规格数据时
            if (!TextUtils.isEmpty(orderMsgBean.getMcDose()) && !TextUtils.isEmpty(orderMsgBean.getMcDoseUnit())) {
                return createSpannable("每次", orderMsgBean.getMcDose(), orderMsgBean.getMcDoseUnit());
            }
            
            // 默认显示克数
            return createSpannable("每次", orderMsgBean.getMcjk(), "g");
        }
        
        // 其他剂型，使用默认的克数显示
        return createSpannable("每次", orderMsgBean.getMcjk(), "g");
    }
    
    /**
     * 显示辅料信息（只有膏方剂型且有辅料数据时才显示）
     * @param orderMsgBean 订单数据
     * @param drugForm 剂型
     */
    private void showAuxiliaryMaterial(OrderMsgBean orderMsgBean, String drugForm) {
        // 移除之前创建的辅料显示组件（如果存在）
        if (llAuxiliaryMaterial != null && llAuxiliaryMaterial.getParent() != null) {
            ((LinearLayout) llAuxiliaryMaterial.getParent()).removeView(llAuxiliaryMaterial);
            llAuxiliaryMaterial = null;
            tvAuxiliaryMaterial = null;
        }
        
        // 只有膏方剂型且有辅料数据时才显示
        if (!PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(drugForm) || 
            orderMsgBean == null || 
            TextUtils.isEmpty(orderMsgBean.getMakeMaterial())) {
            return;
        }
        
        // 创建辅料显示的LinearLayout
        llAuxiliaryMaterial = new LinearLayout(this);
        llAuxiliaryMaterial.setOrientation(LinearLayout.HORIZONTAL);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        layoutParams.setMargins(0, DensityUtils.dip2px(this, 15), 0, 0);
        llAuxiliaryMaterial.setLayoutParams(layoutParams);
        
        // 创建"辅料"标签
        TextView tvLabel = new TextView(this);
        tvLabel.setText("辅　　料");
        tvLabel.setTextSize(15);
        tvLabel.setTextColor(getResources().getColor(R.color.br_color_et_hint));
        LinearLayout.LayoutParams labelParams = new LinearLayout.LayoutParams(
                DensityUtils.dip2px(this, 72), 
                LinearLayout.LayoutParams.WRAP_CONTENT);
        tvLabel.setLayoutParams(labelParams);
        
        // 创建辅料内容TextView
        tvAuxiliaryMaterial = new TextView(this);
        tvAuxiliaryMaterial.setText(orderMsgBean.getMakeMaterial());
        tvAuxiliaryMaterial.setTextSize(15);
        tvAuxiliaryMaterial.setTextColor(getResources().getColor(R.color.br_color_theme_text));
        LinearLayout.LayoutParams contentParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        contentParams.setMargins(DensityUtils.dip2px(this, 18), 0, 0, 0); // 添加18dp左边距，与其他内容对齐
        tvAuxiliaryMaterial.setLayoutParams(contentParams);
        
        // 将标签和内容添加到容器中
        llAuxiliaryMaterial.addView(tvLabel);
        llAuxiliaryMaterial.addView(tvAuxiliaryMaterial);
        
        // 找到主容器LinearLayout，在规格后面插入辅料显示
        LinearLayout mainContainer = (LinearLayout) llSpecification.getParent();
        if (mainContainer != null) {
            // 找到规格容器在主容器中的位置
            int specificationIndex = -1;
            for (int i = 0; i < mainContainer.getChildCount(); i++) {
                if (mainContainer.getChildAt(i) == llSpecification) {
                    specificationIndex = i;
                    break;
                }
            }
            
            // 在规格后面插入辅料显示
            if (specificationIndex >= 0) {
                mainContainer.addView(llAuxiliaryMaterial, specificationIndex + 1);
            } else {
                // 如果没找到规格容器，直接添加到末尾
                mainContainer.addView(llAuxiliaryMaterial);
            }
        }
    }
}
