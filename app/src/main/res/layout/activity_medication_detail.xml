<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_white"
    tools:context="com.doctor.br.activity.MedicationDetailActivity">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:overScrollMode="never"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/color_white"
            android:orientation="vertical"
            android:paddingBottom="74dp">
            <!--         订单编号  -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="@color/color_white"
                android:paddingLeft="15dp"
                android:paddingRight="15dp">

                <TextView
                    android:id="@+id/tv_order_number_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="订单编号："
                    android:textColor="@color/br_color_et_hint"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_order_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toRightOf="@id/tv_order_number_label"
                    android:textColor="@android:color/black"
                    android:textSize="14sp" />

                <ImageView
                    android:id="@+id/btn_copy_order_number"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/document_on_document"
                    android:scaleType="centerInside"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true" />
            </RelativeLayout>
            <!--         姓名    年龄       性别  -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/color_white"
                android:paddingLeft="15dp"
                android:paddingRight="15dp">

                <TextView
                    android:id="@+id/tv_name"
                    style="@style/text_161_15_wrap"
                    android:layout_width="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:layout_toLeftOf="@+id/tv_age"
                    android:singleLine="true" />

                <TextView
                    android:id="@id/tv_age"
                    style="@style/text_161_15_wrap"
                    android:layout_centerInParent="true" />

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true">

                    <TextView
                        android:id="@+id/tv_sex"
                        style="@style/text_161_15_wrap"
                        android:layout_centerVertical="true" />

                    <TextView
                        android:id="@+id/tv_pregant"
                        style="@style/text_ff8_14_wrap"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:layout_toRightOf="@id/tv_sex" />
                </RelativeLayout>
            </RelativeLayout>
            <!--     辨证      -->
            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/color_f6" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="43dp"
                android:background="@color/br_color_white"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/br_margin_30"
                    android:layout_height="38dp"
                    android:src="@drawable/icon_dot_gray" />

                <TextView
                    style="@style/text_1d2_15_wrap"
                    android:text="@string/disease_reason"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_width="@dimen/br_margin_30"
                    android:layout_height="38dp"
                    android:src="@drawable/icon_dot_gray" />
            </LinearLayout>

            <View
                style="@style/line_ea_half"
                android:layout_alignParentBottom="true" />
            <!--                辨证           -->
            <TextView
                android:id="@+id/tv_disease"
                style="@style/text_1d2_15"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_white"
                android:lineSpacingExtra="4dp"
                android:paddingBottom="20dp"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                android:paddingTop="15dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="@color/color_f6" />
            <!--          用药详情             -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="43dp"
                android:background="@color/br_color_white"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/br_margin_30"
                    android:layout_height="38dp"
                    android:src="@drawable/icon_dot_gray" />

                <TextView
                    style="@style/text_1d2_15_wrap"
                    android:text="@string/medicine_detail"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_width="@dimen/br_margin_30"
                    android:layout_height="38dp"
                    android:src="@drawable/icon_dot_gray" />
            </LinearLayout>
            <!--           用药内容         -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_white"
                android:orientation="vertical">

                <View style="@style/line_ea_half" />

                <com.doctor.br.view.FlowLayout
                    android:id="@+id/flowLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/br_color_white"
                    android:orientation="horizontal"
                    android:paddingBottom="5dp"
                    android:paddingTop="5dp"
                    app:layoutDirection="ltr"
                    app:weightDefault="1.0">

                </com.doctor.br.view.FlowLayout>

                <View
                    style="@style/line_ea_half"
                    android:layout_alignParentBottom="true" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                android:paddingTop="9dp">
                <!--      药房代码             -->
                <LinearLayout
                    android:id="@+id/ll_pharmacy_code"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:text="药房代码" />

                    <TextView
                        android:id="@+id/dc_id_tv"
                        style="@style/text_1a_15_wrap"
                        android:layout_marginLeft="8dp" />
                </LinearLayout>
                <!--      药材类型              -->
                <LinearLayout
                    android:id="@+id/ll_medicine_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_tip_medicine_type"
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:text="@string/md_medicine_type" />

                    <TextView
                        android:id="@+id/tv_medicine_type"
                        style="@style/text_1a_15_wrap"
                        android:layout_marginLeft="8dp" />
                </LinearLayout>
                
                <!--      规格              -->
                <LinearLayout
                    android:id="@+id/ll_specification"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tv_tip_specification"
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:text="规　　格" />

                    <TextView
                        android:id="@+id/tv_specification"
                        style="@style/text_1a_15_wrap"
                        android:layout_marginLeft="8dp" />
                </LinearLayout>

                <!--     用法用量        -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_tip_dosage"
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:text="@string/md_dosage" />
                    <!--     用法用量 的具体内容      -->
                    <RelativeLayout
                        android:id="@+id/rl_dosage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:layout_toRightOf="@id/tv_tip_dosage">

                        <TextView
                            android:id="@+id/tv_dose_count"
                            style="@style/text_1a_15_wrap" />

                        <TextView
                            android:id="@+id/tv_dose_day"
                            style="@style/text_1a_15_wrap"
                            android:layout_alignTop="@id/tv_dose_count"
                            android:layout_marginLeft="25dp"
                            android:layout_toRightOf="@id/tv_dose_count" />

                        <TextView
                            android:id="@+id/tv_dose_times"
                            style="@style/text_1a_15_wrap"
                            android:layout_below="@id/tv_dose_count"
                            android:layout_marginTop="10dp" />


                        <TextView
                            android:id="@+id/tv_drink_time"
                            style="@style/text_1a_15_wrap"
                            android:layout_below="@id/tv_dose_times"
                            android:layout_marginTop="10dp" />
                    </RelativeLayout>
                </LinearLayout>
                <!--              用药方法             -->
                <LinearLayout
                    android:id="@+id/ll_use_method"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="15dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_tip_use_method"
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:text="@string/md_use_method" />

                    <TextView
                        android:id="@+id/tv_use_method"
                        style="@style/text_1a_15_wrap"
                        android:layout_marginLeft="8dp" />
                </LinearLayout>
                <!--              服药禁忌             -->
                <LinearLayout
                    android:id="@+id/ll_avoid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_tip_avoid"
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:text="@string/md_avoid" />

                    <TextView
                        android:id="@+id/tv_avoid"
                        style="@style/text_1a_15_wrap"
                        android:layout_marginLeft="8dp" />
                </LinearLayout>
                <!--              补充说明             -->
                <LinearLayout
                    android:id="@+id/ll_guid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_tip_guid"
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:text="@string/md_guid" />

                    <TextView
                        android:id="@+id/tv_guid"
                        style="@style/text_1a_15_wrap"
                        android:layout_marginLeft="8dp" />
                </LinearLayout>
                <!--             是否代煎            -->
                <LinearLayout
                    android:id="@+id/ll_daijian"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tv_tip_daijian"
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:text="是否代煎" />

                    <TextView
                        android:id="@+id/tv_daijian"
                        style="@style/text_1a_15_wrap"
                        android:layout_marginLeft="8dp" />
                </LinearLayout>
                <!--             代煎偏好            -->
                <LinearLayout
                    android:id="@+id/ll_daijian_preference"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tv_tip_daijian_preference"
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:text="代煎偏好" />

                    <TextView
                        android:id="@+id/tv_daijian_preference"
                        style="@style/text_1a_15_wrap"
                        android:layout_marginLeft="8dp" />
                </LinearLayout>

                <!--          订单金额           -->

                <!-- Add secret type section here -->
                <LinearLayout
                    android:id="@+id/ll_secret_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_secret_type"
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:layout_height="match_parent"
                        android:text="保密类型" />

                    <TextView
                        android:id="@+id/tv_secret_type_input"
                        style="@style/text_1a_15_wrap"
                        android:layout_marginLeft="8dp" />
                </LinearLayout>

                <!--          订单金额           -->
                <LinearLayout
                    android:id="@+id/ll_money"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_tip_money"
                        style="@style/text_b1_15_wrap"
                        android:layout_width="80dp"
                        android:text="@string/md_order_money" />

                    <TextView
                        android:id="@+id/tv_money"
                        style="@style/WH_wrap_wrap"
                        android:layout_alignBottom="@id/tv_tip_money"
                        android:layout_marginLeft="8dp"
                        android:layout_toRightOf="@id/tv_tip_money"
                        android:textColor="@color/br_color_tab_checked"
                        android:textSize="@dimen/textsize_16" />
                </LinearLayout>

            </LinearLayout>
            <!--     暗语      -->
            <LinearLayout
                android:id="@+id/ll_secret"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="@color/color_f6" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="43dp"
                    android:background="@color/br_color_white"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/br_margin_30"
                        android:layout_height="38dp"
                        android:src="@drawable/icon_dot_gray" />

                    <TextView
                        style="@style/text_1d2_15_wrap"
                        android:text="按语"
                        android:textStyle="bold" />

                    <ImageView
                        android:layout_width="@dimen/br_margin_30"
                        android:layout_height="38dp"
                        android:src="@drawable/icon_dot_gray" />
                </LinearLayout>

                <View
                    style="@style/line_ea_half"
                    android:layout_alignParentBottom="true" />
                <!--   暗语的具体内容         -->
                <TextView
                    android:id="@+id/tv_secret"
                    style="@style/text_1d2_15"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/color_white"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:paddingTop="15dp" />
            </LinearLayout>

            <!--     原拍方照片      -->
            <LinearLayout
                android:id="@+id/picture_linear"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="@color/color_f6" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="43dp"
                    android:background="@color/br_color_white"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/br_margin_30"
                        android:layout_height="38dp"
                        android:src="@drawable/icon_dot_gray" />

                    <TextView
                        style="@style/text_1d2_15_wrap"
                        android:text="原拍方照片"
                        android:textStyle="bold" />

                    <ImageView
                        android:layout_width="@dimen/br_margin_30"
                        android:layout_height="38dp"
                        android:src="@drawable/icon_dot_gray" />
                </LinearLayout>

                <View
                    style="@style/line_ea_half"
                    android:layout_alignParentBottom="true" />
                <!--   暗语的具体内容         -->
                <ImageView
                    android:id="@+id/picture_img"
                    android:layout_width="match_parent"
                    android:layout_height="185dp"
                    android:layout_marginBottom="17dp"
                    android:layout_marginLeft="30dp"
                    android:layout_marginRight="30dp"
                    android:layout_marginTop="15dp"
                    android:background="@color/color_white"
                    tools:src="@drawable/default_head_img" />

            </LinearLayout>
        </LinearLayout>
    </ScrollView>
    <!--          修改剂数              作废         -->
    <RelativeLayout
        android:id="@+id/rl_bottom"
        android:layout_width="match_parent"
        android:layout_height="57dp"
        android:layout_alignParentBottom="true"
        android:background="@color/color_white">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/br_color_inner_line" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="30dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="80dp"
            android:background="@color/br_color_inner_line" />

        <LinearLayout
            android:id="@+id/ll_delete"
            android:layout_width="80dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/medication_delete" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:includeFontPadding="false"
                android:text="作废"
                android:textColor="@color/color_1a"
                android:textSize="@dimen/textsize_13" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_modify"
            android:layout_width="80dp"
            android:layout_height="match_parent"
            android:layout_toRightOf="@id/ll_delete"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_modify"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/medication_modify" />

            <TextView
                android:id="@+id/tv_modify"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:includeFontPadding="false"
                android:text="修改剂数"
                android:textColor="@color/color_1a"
                android:textSize="@dimen/textsize_13" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_clip"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toRightOf="@id/ll_modify"
            android:background="@color/br_color_theme"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="复制药方"
                android:textColor="@color/color_white"
                android:textSize="@dimen/textsize_16" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="9dp"
                android:layout_marginRight="9dp"
                android:src="@drawable/medication_clip" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="添加药材"
                android:textColor="@color/color_white"
                android:textSize="@dimen/textsize_16" />
        </LinearLayout>

        <Button
            android:visibility="gone"
            android:id="@+id/send_btn"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_toEndOf="@id/ll_delete"
            android:layout_toRightOf="@id/ll_delete"
            android:background="@color/br_color_theme"
            android:text="发    送"
            android:textColor="@color/br_color_white"
            android:textSize="@dimen/textsize_16" />
    </RelativeLayout>

    <org.newapp.ones.base.widgets.EmptyView
        android:id="@+id/emptyView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"></org.newapp.ones.base.widgets.EmptyView>
</RelativeLayout>
